/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.constant;

/**
 * SIM卡异常告警常量
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-27
 */
public class SimCardAlarmConstant {

    /**
     * SIM卡在线状态枚举值
     */
    public static final class OnlineStatus {
        /** 未知 */
        public static final int UNKNOWN = 0;
        /** 离线 */
        public static final int OFFLINE = 1;
        /** 在线 */
        public static final int ONLINE = 2;
    }

    /**
     * 网络制式
     */
    public static final class NetworkStandard {
        /** 4G网络制式 */
        public static final String LTE = "LTE";
        /** 5G网络制式 */
        public static final String NR_5G = "5G NR";
    }

    /**
     * 持续时间常量（秒）
     */
    public static final class Duration {
        /** 4G状态持续时间阈值：10秒 */
        public static final int LTE_DURATION_THRESHOLD = 10;
        /** 5G信号质量差持续时间阈值：5秒 */
        public static final int NR_5G_SIGNAL_DURATION_THRESHOLD = 5;
    }

    /**
     * 5G信号质量阈值
     */
    public static final class SignalThreshold {
        /** RSRP阈值：-100dBm */
        public static final float RSRP_THRESHOLD = -100.0f;
        /** SINR阈值：3dB */
        public static final float SINR_THRESHOLD = 3.0f;
    }

    /**
     * Redis ZSet 过期时间（秒）
     */
    public static final class RedisExpire {
        /** ZSet过期时间：1小时 */
        public static final long ZSET_EXPIRE_TIME = 3600;
    }

    /**
     * 告警类型标识
     */
    public static final class AlarmType {
        /** SIM卡离线告警 */
        public static final String SIM_OFFLINE = "SIM_OFFLINE";
        /** SIM卡4G状态告警 */
        public static final String SIM_4G_STATUS = "SIM_4G_STATUS";
        /** SIM卡5G信号质量告警 */
        public static final String SIM_5G_SIGNAL = "SIM_5G_SIGNAL";
    }
}
