/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.manager.alarm;

import com.jdx.rover.server.api.domain.dto.hardware.data.SimDataDTO;
import com.jdx.rover.server.api.domain.dto.hardware.status.SimStatusDTO;
import com.jdx.rover.server.business.constant.SimCardAlarmConstant;
import com.jdx.rover.server.business.manager.report.VehicleAbnormalReportManager;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SIM卡异常告警管理器测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-27
 */
@ExtendWith(MockitoExtension.class)
class SimCardAbnormalAlarmManagerTest {

    @Mock
    private VehicleAbnormalReportManager vehicleAbnormalReportManager;

    @InjectMocks
    private SimCardAbnormalAlarmManager simCardAbnormalAlarmManager;

    private static final String TEST_VEHICLE_NAME = "TEST_VEHICLE_001";
    private static final Integer TEST_DEVICE_ID = 1;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void testProcessSimStatusChange_OfflineAlarm() {
        try (MockedStatic<RedissonUtils> mockedRedissonUtils = mockStatic(RedissonUtils.class)) {
            // 准备测试数据 - SIM卡离线
            SimStatusDTO simStatus = new SimStatusDTO();
            simStatus.setOnlineStatus(SimCardAlarmConstant.OnlineStatus.OFFLINE);
            simStatus.setDeviceId(TEST_DEVICE_ID);
            simStatus.setRecordTime(new Date());

            List<SimStatusDTO> simStatusList = Arrays.asList(simStatus);

            // 执行测试
            simCardAbnormalAlarmManager.processSimStatusChange(TEST_VEHICLE_NAME, simStatusList);

            // 验证告警生成
            verify(vehicleAbnormalReportManager, times(1)).saveRedisAndSendJmq(any(), eq(TEST_VEHICLE_NAME));
        }
    }

    @Test
    void testProcessSimStatusChange_OnlineAlarm() {
        try (MockedStatic<RedissonUtils> mockedRedissonUtils = mockStatic(RedissonUtils.class)) {
            // 准备测试数据 - SIM卡在线
            SimStatusDTO simStatus = new SimStatusDTO();
            simStatus.setOnlineStatus(SimCardAlarmConstant.OnlineStatus.ONLINE);
            simStatus.setDeviceId(TEST_DEVICE_ID);
            simStatus.setRecordTime(new Date());

            List<SimStatusDTO> simStatusList = Arrays.asList(simStatus);

            // 执行测试
            simCardAbnormalAlarmManager.processSimStatusChange(TEST_VEHICLE_NAME, simStatusList);

            // 验证不会生成告警（在线状态只会清除告警）
            verify(vehicleAbnormalReportManager, never()).saveRedisAndSendJmq(any(), any());
        }
    }

    @Test
    void testProcessSimDataChange_4GAlarm() {
        try (MockedStatic<RedissonUtils> mockedRedissonUtils = mockStatic(RedissonUtils.class)) {
            // 模拟Redis操作
            mockedRedissonUtils.when(() -> RedissonUtils.addToZSet(anyString(), anyDouble(), anyString())).then(invocation -> null);
            mockedRedissonUtils.when(() -> RedissonUtils.expireZSet(anyString(), anyLong())).then(invocation -> null);
            mockedRedissonUtils.when(() -> RedissonUtils.removeZSetByScore(anyString(), anyDouble(), anyDouble())).thenReturn(0);
            mockedRedissonUtils.when(() -> RedissonUtils.countZSetByScore(anyString(), anyDouble(), anyDouble()))
                    .thenReturn(SimCardAlarmConstant.Duration.LTE_DURATION_THRESHOLD);

            // 准备测试数据 - 4G网络
            SimDataDTO simData = SimDataDTO.builder()
                    .vehicleName(TEST_VEHICLE_NAME)
                    .standard(SimCardAlarmConstant.NetworkStandard.LTE)
                    .deviceId(TEST_DEVICE_ID)
                    .recordTime(new Date())
                    .build();

            List<SimDataDTO> simDataList = Arrays.asList(simData);

            // 执行测试
            simCardAbnormalAlarmManager.processSimDataChange(TEST_VEHICLE_NAME, simDataList);

            // 验证Redis操作
            mockedRedissonUtils.verify(() -> RedissonUtils.addToZSet(anyString(), anyDouble(), anyString()), times(1));
            mockedRedissonUtils.verify(() -> RedissonUtils.countZSetByScore(anyString(), anyDouble(), anyDouble()), times(1));

            // 验证告警生成
            verify(vehicleAbnormalReportManager, times(1)).saveRedisAndSendJmq(any(), eq(TEST_VEHICLE_NAME));
        }
    }

    @Test
    void testProcessSimDataChange_5GSignalPoorAlarm() {
        try (MockedStatic<RedissonUtils> mockedRedissonUtils = mockStatic(RedissonUtils.class)) {
            // 模拟Redis操作
            mockedRedissonUtils.when(() -> RedissonUtils.addToZSet(anyString(), anyDouble(), anyString())).then(invocation -> null);
            mockedRedissonUtils.when(() -> RedissonUtils.expireZSet(anyString(), anyLong())).then(invocation -> null);
            mockedRedissonUtils.when(() -> RedissonUtils.removeZSetByScore(anyString(), anyDouble(), anyDouble())).thenReturn(0);
            mockedRedissonUtils.when(() -> RedissonUtils.countZSetByScore(anyString(), anyDouble(), anyDouble()))
                    .thenReturn(SimCardAlarmConstant.Duration.NR_5G_SIGNAL_DURATION_THRESHOLD);

            // 准备测试数据 - 5G网络信号差
            SimDataDTO simData = SimDataDTO.builder()
                    .vehicleName(TEST_VEHICLE_NAME)
                    .standard(SimCardAlarmConstant.NetworkStandard.NR_5G)
                    .rsrp(-105.0f) // 低于阈值-100
                    .sinr(2.0f)    // 低于阈值3
                    .deviceId(TEST_DEVICE_ID)
                    .recordTime(new Date())
                    .build();

            List<SimDataDTO> simDataList = Arrays.asList(simData);

            // 执行测试
            simCardAbnormalAlarmManager.processSimDataChange(TEST_VEHICLE_NAME, simDataList);

            // 验证Redis操作
            mockedRedissonUtils.verify(() -> RedissonUtils.addToZSet(anyString(), anyDouble(), anyString()), times(1));
            mockedRedissonUtils.verify(() -> RedissonUtils.countZSetByScore(anyString(), anyDouble(), anyDouble()), times(1));

            // 验证告警生成
            verify(vehicleAbnormalReportManager, times(1)).saveRedisAndSendJmq(any(), eq(TEST_VEHICLE_NAME));
        }
    }

    @Test
    void testProcessSimDataChange_5GSignalGoodNoAlarm() {
        try (MockedStatic<RedissonUtils> mockedRedissonUtils = mockStatic(RedissonUtils.class)) {
            // 模拟Redis操作
            mockedRedissonUtils.when(() -> RedissonUtils.zsetExists(anyString())).thenReturn(true);
            mockedRedissonUtils.when(() -> RedissonUtils.deleteObject(anyString())).thenReturn(true);

            // 准备测试数据 - 5G网络信号良好
            SimDataDTO simData = SimDataDTO.builder()
                    .vehicleName(TEST_VEHICLE_NAME)
                    .standard(SimCardAlarmConstant.NetworkStandard.NR_5G)
                    .rsrp(-95.0f) // 高于阈值-100
                    .sinr(5.0f)   // 高于阈值3
                    .deviceId(TEST_DEVICE_ID)
                    .recordTime(new Date())
                    .build();

            List<SimDataDTO> simDataList = Arrays.asList(simData);

            // 执行测试
            simCardAbnormalAlarmManager.processSimDataChange(TEST_VEHICLE_NAME, simDataList);

            // 验证清除告警操作
            mockedRedissonUtils.verify(() -> RedissonUtils.zsetExists(anyString()), times(1));
            mockedRedissonUtils.verify(() -> RedissonUtils.deleteObject(anyString()), times(1));

            // 验证不会生成新告警
            verify(vehicleAbnormalReportManager, never()).saveRedisAndSendJmq(any(), any());
        }
    }

    @Test
    void testProcessSimDataChange_Non4GClearAlarm() {
        try (MockedStatic<RedissonUtils> mockedRedissonUtils = mockStatic(RedissonUtils.class)) {
            // 模拟Redis操作
            mockedRedissonUtils.when(() -> RedissonUtils.zsetExists(anyString())).thenReturn(true);
            mockedRedissonUtils.when(() -> RedissonUtils.deleteObject(anyString())).thenReturn(true);

            // 准备测试数据 - 非4G网络
            SimDataDTO simData = SimDataDTO.builder()
                    .vehicleName(TEST_VEHICLE_NAME)
                    .standard(SimCardAlarmConstant.NetworkStandard.NR_5G)
                    .deviceId(TEST_DEVICE_ID)
                    .recordTime(new Date())
                    .build();

            List<SimDataDTO> simDataList = Arrays.asList(simData);

            // 执行测试
            simCardAbnormalAlarmManager.processSimDataChange(TEST_VEHICLE_NAME, simDataList);

            // 验证清除4G告警操作
            mockedRedissonUtils.verify(() -> RedissonUtils.zsetExists(anyString()), atLeastOnce());
        }
    }

    @Test
    void testProcessSimDataChange_EmptyList() {
        // 准备测试数据 - 空列表
        List<SimDataDTO> simDataList = Arrays.asList();

        // 执行测试
        simCardAbnormalAlarmManager.processSimDataChange(TEST_VEHICLE_NAME, simDataList);

        // 验证不会有任何操作
        verify(vehicleAbnormalReportManager, never()).saveRedisAndSendJmq(any(), any());
    }

    @Test
    void testProcessSimStatusChange_EmptyList() {
        // 准备测试数据 - 空列表
        List<SimStatusDTO> simStatusList = Arrays.asList();

        // 执行测试
        simCardAbnormalAlarmManager.processSimStatusChange(TEST_VEHICLE_NAME, simStatusList);

        // 验证不会有任何操作
        verify(vehicleAbnormalReportManager, never()).saveRedisAndSendJmq(any(), any());
    }
}
